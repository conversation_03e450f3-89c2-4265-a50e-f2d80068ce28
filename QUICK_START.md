# POKEMMO 孵蛋计算器 v2.0 快速开始

## 🚀 立即开始

### 1. 配置数据库
编辑 `config.py` 文件，修改数据库密码：
```python
DATABASE_CONFIG = {
    "host": "localhost",
    "port": 3306,
    "user": "root",
    "password": "您的MySQL密码",  # 修改这里！
    "database": "pokemmo_db",
    "charset": "utf8mb4"
}
```

### 2. 启动程序
```bash
python pokemmo_breeding_calculator.py
```

### 3. 首次运行
- 程序会自动创建数据库和表
- 如果数据库连接失败，会显示设置指导对话框
- 可以选择重试连接或退出程序

## 🎮 主要功能

### 宝可梦管理
- **卡片式布局**：现代化的网格显示
- **高级筛选**：按名称、蛋组、V数、状态筛选
- **锁定功能**：保护珍贵宝可梦不被用于繁殖
- **详细信息**：点击卡片查看完整信息

### 繁殖计算
- **目标设置**：选择宝可梦种类和目标个体值
- **智能计算**：自动计算最优繁殖路径
- **成本分析**：显示繁殖成本和时间

### 数据管理
- **MySQL数据库**：可靠的数据存储
- **自动备份**：定期备份重要数据
- **导入导出**：支持数据迁移

## 🎨 界面特色

### 现代化设计
- **蓝色主题**：专业的配色方案
- **卡片布局**：清晰的信息展示
- **响应式**：适配不同屏幕尺寸

### 交互体验
- **悬停效果**：鼠标悬停时的视觉反馈
- **加载动画**：优雅的等待提示
- **状态图标**：直观的状态显示

## 🔧 故障排除

### 数据库连接失败
1. 确认MySQL服务正在运行
2. 检查用户名和密码是否正确
3. 确认端口3306未被占用
4. 检查防火墙设置

### 程序启动缓慢
- 首次运行需要初始化数据库
- 大量图片需要时间加载
- 检查硬盘空间是否充足

### 图片无法显示
- 将宝可梦图片放入 `images` 目录
- 支持格式：`.gif`, `.png`, `.jpg`, `.jpeg`
- 命名格式：`001妙蛙种子.gif` 或 `妙蛙种子.gif`

## 📞 获取帮助

如果遇到问题：
1. 查看控制台错误信息
2. 检查配置文件设置
3. 确认依赖包已安装
4. 重启MySQL服务

## 🎯 下一步

程序运行后，您可以：
1. 添加您的宝可梦数据
2. 设置繁殖目标
3. 计算最优方案
4. 管理您的宝可梦收藏

享受现代化的POKEMMO繁殖管理体验！
