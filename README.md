# POKEMMO 孵蛋计算器

这是一个用于计算 POKEMMO 游戏中孵蛋成本的工具，现已升级为基于数据库的完整宝可梦管理系统。

## 主要功能

### 宝可梦管理
- 从图片文件自动识别宝可梦种类（支持图鉴编号+名称格式）
- 添加和管理个人宝可梦收藏
- 设置个体值（IVs）、性格、性别、价格等详细信息
- 支持宝可梦蛋组信息管理
- 支持昵称和备注功能
- 闪光宝可梦标记

### 繁殖计算
- 智能繁殖方案计算
- 基于现有宝可梦素材的最优配对
- 考虑个体值、性格、蛋组兼容性
- 成本分析和优化建议
- 支持性别锁定成本计算

## 系统要求

- Python 3.7+
- MySQL 5.7+ 或 MariaDB 10.2+
- 推荐内存：4GB+

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置数据库

确保MySQL服务正在运行，并使用以下默认配置：
- 主机：localhost
- 用户名：root
- 密码：XZJ020601
- 端口：3306

如需修改数据库配置，请编辑 `database.py` 文件中的 `DatabaseManager` 类初始化参数。

### 3. 准备宝可梦图片

将宝可梦图片放入 `images` 目录，文件命名格式：
- `001妙蛙种子.gif` （图鉴编号+名称）
- `妙蛙种子.gif` （仅名称）

支持的图片格式：`.gif`, `.png`, `.jpg`, `.jpeg`

### 4. 运行程序

```bash
python pokemmo_breeding_calculator.py
```

首次运行时，程序会自动：
- 创建数据库和表结构
- 初始化蛋组数据
- 扫描并导入宝可梦种类信息

## 使用指南

### 宝可梦管理

1. **添加宝可梦**：
   - 点击"添加宝可梦"按钮
   - 在种类选择对话框中搜索并选择宝可梦
   - 设置个体值、性格、性别、价格等信息
   - 可添加昵称和备注

2. **编辑宝可梦**：
   - 在列表中选择要编辑的宝可梦
   - 点击"编辑宝可梦"按钮
   - 修改相关信息并保存

3. **查看详情**：
   - 选择列表中的宝可梦查看详细信息
   - 包括图片、个体值、蛋组等信息

### 繁殖计算

1. **设置目标**：
   - 在"繁殖计算"标签页中设置目标个体值
   - 选择目标性格和性别（可选）
   - 设置公母比例

2. **选择素材**：
   - 选择"使用所有宝可梦素材"或手动选择特定宝可梦
   - 程序会基于蛋组兼容性进行筛选

3. **计算方案**：
   - 点击"计算最优繁殖方案"
   - 查看推荐的繁殖路径和成本分析

## 数据库结构

程序使用MySQL数据库存储以下信息：

### 主要数据表

- **pokemon_species**: 宝可梦种类基础信息
- **pokemon_instances**: 用户的宝可梦实例
- **egg_groups**: 蛋组信息
- **pokemon_egg_group_relations**: 宝可梦与蛋组的关系

### 蛋组系统

程序支持完整的蛋组系统，包括：
- 怪兽、水中1/2/3、虫、飞行、陆上、妖精
- 植物、人形、矿物、不定形、龙
- 未发现、性别不明

繁殖计算会自动考虑蛋组兼容性。

## 特色功能

### 智能繁殖计算
- 基于现有宝可梦素材的最优配对算法
- 考虑个体值匹配度和成本效益
- 支持多代繁殖路径规划

### 图片自动识别
- 支持图鉴编号+名称的文件命名格式
- 自动解析并导入宝可梦种类信息
- 支持多种图片格式

### 完整的数据管理
- 数据持久化存储
- 支持数据备份和恢复
- 高效的查询和搜索功能

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否运行
   - 确认用户名和密码正确
   - 检查防火墙设置

2. **图片无法显示**
   - 确认图片文件格式正确
   - 检查文件路径是否存在
   - 确认图片文件未损坏

3. **繁殖计算无结果**
   - 确保已添加足够的宝可梦素材
   - 检查目标设置是否合理
   - 验证蛋组兼容性

## 更新日志

### v2.0.0 (当前版本)
- ✅ 重构为基于数据库的架构
- ✅ 添加完整的宝可梦管理系统
- ✅ 支持蛋组信息和兼容性检查
- ✅ 改进的图片文件名解析
- ✅ 新的用户界面设计
- ✅ 智能繁殖方案计算

### v1.0.0
- 基础的孵蛋成本计算功能
- JSON文件数据存储
- 简单的宝可梦管理

## 技术栈

- **后端**: Python 3.7+
- **数据库**: MySQL/MariaDB
- **GUI**: Tkinter
- **图片处理**: Pillow
- **数据库连接**: PyMySQL