# POKEMMO 孵蛋计算器 v2.0 使用指南

## 🚀 快速开始

### 系统要求
- Python 3.7+
- MySQL 5.7+ 或 MariaDB 10.2+
- 内存：4GB+
- 硬盘：500MB+

### 安装步骤

1. **安装Python依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **配置数据库**
   - 确保MySQL服务正在运行
   - 编辑 `config.py` 文件中的数据库配置：
   ```python
   DATABASE_CONFIG = {
       "host": "localhost",
       "port": 3306,
       "user": "root",
       "password": "您的MySQL密码",  # 修改这里
       "database": "pokemmo_db",
       "charset": "utf8mb4"
   }
   ```

3. **准备宝可梦图片**
   - 将宝可梦图片放入 `images` 目录
   - 支持的命名格式：
     - `001妙蛙种子.gif` （图鉴编号+名称）
     - `妙蛙种子.gif` （仅名称）
   - 支持格式：`.gif`, `.png`, `.jpg`, `.jpeg`

4. **启动程序**
   ```bash
   python pokemmo_breeding_calculator.py
   ```

## 🎮 功能介绍

### 宝可梦管理

#### 界面布局
```
┌─────────────┬─────────────────────┬─────────────┐
│  筛选面板   │    宝可梦卡片网格    │  详情面板   │
│             │                     │             │
│ 🔍 搜索     │  ┌───┐ ┌───┐ ┌───┐  │ 📊 基本信息 │
│ 🥚 蛋组筛选 │  │🎮 │ │🎮 │ │🎮 │  │ 💎 个体值   │
│ ⭐ V数筛选  │  └───┘ └───┘ └───┘  │ 🧬 蛋组     │
│ 🔒 状态筛选 │  ┌───┐ ┌───┐ ┌───┐  │ 📝 备注     │
│             │  │🎮 │ │🎮 │ │🎮 │  │ 🔒 锁定     │
└─────────────┴─────────────────────┴─────────────┘
```

#### 添加宝可梦
1. 点击 **"+ 添加"** 按钮
2. 在弹出的对话框中搜索并选择宝可梦种类
3. 设置个体信息：
   - **昵称**：可选，便于识别
   - **性别**：公/母/不明
   - **性格**：影响属性成长
   - **个体值**：0-31，31为满值
   - **价格**：市场价格
   - **闪光**：是否为闪光宝可梦
   - **备注**：额外说明

#### 宝可梦卡片说明
- **图片**：显示宝可梦图像
- **名称**：昵称或种类名称
- **V数标签**：
  - 🟢 5V+ (绿色)
  - 🔘 其他 (灰色)
- **状态图标**：
  - 🔒 已锁定
  - ✨ 闪光

#### 筛选功能
- **文本搜索**：按名称或昵称搜索
- **蛋组筛选**：选择特定蛋组
- **V数筛选**：按个体值V数筛选
- **状态筛选**：显示/隐藏锁定或闪光宝可梦

#### 锁定功能
- **用途**：保护珍贵宝可梦不被用于繁殖计算
- **操作**：选中宝可梦后点击详情面板中的锁定按钮
- **标识**：锁定的宝可梦卡片显示🔒图标

### 繁殖计算

#### 设置目标
1. **选择目标宝可梦种类**
   - 点击 **"🔍 选择宝可梦种类"** 按钮
   - 搜索并选择要繁殖的宝可梦

2. **设置目标个体值**
   - 使用切换按钮设置每个属性的目标值
   - 🟢 绿色按钮 = 31 (满值)
   - 🔘 灰色按钮 = 0 (不要求)

3. **选择繁殖参数**
   - 目标性别（可选）
   - 目标性格
   - 公母比例

#### 计算过程
1. 程序自动筛选可用的繁殖素材
2. 考虑蛋组兼容性
3. 排除锁定的宝可梦
4. 计算最优繁殖路径
5. 显示成本分析

## 🎨 界面特色

### 现代化设计
- **渐变标题栏**：蓝色渐变背景
- **卡片式布局**：现代化的宝可梦展示
- **圆角按钮**：多种颜色样式
- **焦点效果**：输入框获得焦点时高亮
- **加载动画**：旋转加载指示器

### 颜色系统
- **主色调**：`#3b82f6` (蓝色)
- **成功色**：`#10b981` (绿色)
- **警告色**：`#f59e0b` (橙色)
- **危险色**：`#ef4444` (红色)
- **信息色**：`#06b6d4` (青色)

### 响应式布局
- 自适应窗口大小
- 最小尺寸：1200x800
- 推荐尺寸：1400x900

## 🔧 高级设置

### 配置文件 (config.py)
```python
# 数据库配置
DATABASE_CONFIG = {
    "host": "localhost",
    "port": 3306,
    "user": "root", 
    "password": "您的密码",
    "database": "pokemmo_db"
}

# UI配置
UI_CONFIG = {
    "window_size": "1400x900",
    "theme": "modern"
}

# 繁殖计算配置
BREEDING_CONFIG = {
    "default_base_cost": 1000,
    "gender_lock_multiplier": 2.0,
    "max_generations": 10
}
```

### 数据库管理
- **自动创建**：首次运行自动创建数据库和表
- **数据迁移**：支持从旧版本升级
- **备份建议**：定期备份数据库

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否运行
   - 确认用户名和密码正确
   - 检查防火墙设置

2. **图片无法显示**
   - 确认图片文件格式正确
   - 检查文件路径是否存在
   - 确认图片文件未损坏

3. **程序启动缓慢**
   - 首次运行需要初始化数据库
   - 大量图片需要时间加载
   - 检查硬盘空间是否充足

### 错误代码
- **DB001**：数据库连接失败
- **DB002**：表创建失败
- **IMG001**：图片加载失败
- **UI001**：界面初始化失败

## 📞 技术支持

如遇到问题，请提供以下信息：
- 操作系统版本
- Python版本
- MySQL版本
- 错误截图
- 错误日志

## 🔄 更新日志

### v2.0.0 (当前版本)
- ✅ 全新现代化UI设计
- ✅ 宝可梦卡片网格布局
- ✅ 高级筛选功能
- ✅ 宝可梦锁定系统
- ✅ 数据库架构重构
- ✅ 渐变和动画效果

### v1.0.0
- 基础繁殖计算功能
- JSON文件数据存储
- 简单列表界面
