#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
POKEMMO 孵蛋计算器配置文件
"""

# 数据库配置
DATABASE_CONFIG = {
    "host": "localhost",
    "port": 3306,
    "user": "root",
    "password": "XZJ020601",  # 请修改为您的MySQL密码
    "database": "pokemmo_db",
    "charset": "utf8mb4"
}

# UI主题配置
UI_CONFIG = {
    "window_size": "1400x900",
    "min_window_size": (1200, 800),
    "theme": "modern",  # modern, classic, dark
    "language": "zh_CN",  # zh_CN, en_US
}

# 应用配置
APP_CONFIG = {
    "auto_backup": True,
    "backup_interval": 24,  # 小时
    "max_backups": 10,
    "auto_save": True,
    "debug_mode": False,
}

# 图片配置
IMAGE_CONFIG = {
    "supported_formats": [".gif", ".png", ".jpg", ".jpeg"],
    "default_size": (60, 60),
    "card_size": (120, 150),
    "cache_images": True,
    "max_cache_size": 100,  # MB
}

# 繁殖计算配置
BREEDING_CONFIG = {
    "default_base_cost": 1000,
    "gender_lock_multiplier": 2.0,
    "shiny_multiplier": 10.0,
    "max_generations": 10,
    "consider_nature": True,
    "consider_ability": False,
}

# 导出配置
EXPORT_CONFIG = {
    "default_format": "json",
    "include_images": False,
    "compress_export": True,
}

# 日志配置
LOG_CONFIG = {
    "level": "INFO",  # DEBUG, INFO, WARNING, ERROR
    "file": "pokemmo_calculator.log",
    "max_size": 10,  # MB
    "backup_count": 5,
}
