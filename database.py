import pymysql
import json
from typing import List, Dict, Optional, Tuple

class DatabaseManager:
    """数据库管理类"""
    
    def __init__(self, host='localhost', user='root', password='XZJ020601', database='pokemmo_db'):
        self.host = host
        self.user = user
        self.password = password
        self.database = database
        self.connection = None
        
    def connect(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            return True
        except pymysql.Error as e:
            print(f"数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def create_database_if_not_exists(self):
        """创建数据库（如果不存在）"""
        try:
            # 连接到MySQL服务器（不指定数据库）
            temp_connection = pymysql.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                charset='utf8mb4'
            )
            
            with temp_connection.cursor() as cursor:
                cursor.execute(f"CREATE DATABASE IF NOT EXISTS {self.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                temp_connection.commit()
            
            temp_connection.close()
            return True
        except pymysql.Error as e:
            print(f"创建数据库失败: {e}")
            return False
    
    def create_tables(self):
        """创建数据库表"""
        if not self.connection:
            return False
            
        try:
            with self.connection.cursor() as cursor:
                # 创建蛋组表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS egg_groups (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        name VARCHAR(50) NOT NULL UNIQUE,
                        description TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """)
                
                # 创建宝可梦种类表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS pokemon_species (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        pokedex_number INT NOT NULL UNIQUE,
                        name VARCHAR(100) NOT NULL,
                        image_path VARCHAR(255),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_pokedex_number (pokedex_number),
                        INDEX idx_name (name)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """)
                
                # 创建宝可梦蛋组关系表
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS pokemon_egg_group_relations (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        pokemon_species_id INT NOT NULL,
                        egg_group_id INT NOT NULL,
                        FOREIGN KEY (pokemon_species_id) REFERENCES pokemon_species(id) ON DELETE CASCADE,
                        FOREIGN KEY (egg_group_id) REFERENCES egg_groups(id) ON DELETE CASCADE,
                        UNIQUE KEY unique_pokemon_egg_group (pokemon_species_id, egg_group_id)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """)
                
                # 创建宝可梦实例表（用户添加的具体宝可梦）
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS pokemon_instances (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        pokemon_species_id INT NOT NULL,
                        nickname VARCHAR(100),
                        gender ENUM('公', '母', '不明') DEFAULT '不明',
                        nature VARCHAR(50) DEFAULT 'Hardy',
                        hp_iv INT DEFAULT 0 CHECK (hp_iv >= 0 AND hp_iv <= 31),
                        attack_iv INT DEFAULT 0 CHECK (attack_iv >= 0 AND attack_iv <= 31),
                        defense_iv INT DEFAULT 0 CHECK (defense_iv >= 0 AND defense_iv <= 31),
                        sp_attack_iv INT DEFAULT 0 CHECK (sp_attack_iv >= 0 AND sp_attack_iv <= 31),
                        sp_defense_iv INT DEFAULT 0 CHECK (sp_defense_iv >= 0 AND sp_defense_iv <= 31),
                        speed_iv INT DEFAULT 0 CHECK (speed_iv >= 0 AND speed_iv <= 31),
                        price INT DEFAULT 0,
                        is_shiny BOOLEAN DEFAULT FALSE,
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (pokemon_species_id) REFERENCES pokemon_species(id) ON DELETE CASCADE,
                        INDEX idx_species (pokemon_species_id),
                        INDEX idx_gender (gender),
                        INDEX idx_price (price)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """)
                
                self.connection.commit()
                return True
                
        except pymysql.Error as e:
            print(f"创建表失败: {e}")
            self.connection.rollback()
            return False
    
    def initialize_egg_groups(self):
        """初始化蛋组数据"""
        if not self.connection:
            return False
            
        egg_groups_data = [
            ('怪兽', '类似怪兽的宝可梦'),
            ('水中1', '水中生活的宝可梦（鱼类等）'),
            ('水中2', '水中生活的宝可梦（两栖类等）'),
            ('水中3', '水中生活的宝可梦（无脊椎动物等）'),
            ('虫', '昆虫类宝可梦'),
            ('飞行', '会飞行的宝可梦'),
            ('陆上', '陆地上生活的哺乳动物类宝可梦'),
            ('妖精', '妖精类宝可梦'),
            ('植物', '植物类宝可梦'),
            ('人形', '人形宝可梦'),
            ('矿物', '矿物类宝可梦'),
            ('不定形', '形状不固定的宝可梦'),
            ('龙', '龙类宝可梦'),
            ('未发现', '无法繁殖的宝可梦'),
            ('性别不明', '无性别的宝可梦')
        ]
        
        try:
            with self.connection.cursor() as cursor:
                for name, description in egg_groups_data:
                    cursor.execute(
                        "INSERT IGNORE INTO egg_groups (name, description) VALUES (%s, %s)",
                        (name, description)
                    )
                self.connection.commit()
                return True
        except pymysql.Error as e:
            print(f"初始化蛋组数据失败: {e}")
            self.connection.rollback()
            return False
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """执行查询语句"""
        if not self.connection:
            return []
            
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query, params)
                return cursor.fetchall()
        except pymysql.Error as e:
            print(f"查询执行失败: {e}")
            return []
    
    def execute_update(self, query: str, params: tuple = None) -> tuple:
        """执行更新语句，返回(成功标志, 最后插入的ID)"""
        if not self.connection:
            return False, 0

        try:
            with self.connection.cursor() as cursor:
                cursor.execute(query, params)
                self.connection.commit()
                return True, cursor.lastrowid
        except pymysql.Error as e:
            print(f"更新执行失败: {e}")
            self.connection.rollback()
            return False, 0

    def get_last_insert_id(self) -> int:
        """获取最后插入的ID（已弃用，使用execute_update的返回值）"""
        if not self.connection:
            return 0
        return 0  # 这个方法不再可靠，应该使用execute_update的返回值

    # 宝可梦种类相关操作
    def add_pokemon_species(self, pokedex_number: int, name: str, image_path: str = None) -> int:
        """添加宝可梦种类"""
        query = "INSERT INTO pokemon_species (pokedex_number, name, image_path) VALUES (%s, %s, %s)"
        success, insert_id = self.execute_update(query, (pokedex_number, name, image_path))
        return insert_id if success else 0

    def get_pokemon_species_by_name(self, name: str) -> Optional[Dict]:
        """根据名称获取宝可梦种类"""
        query = "SELECT * FROM pokemon_species WHERE name = %s"
        results = self.execute_query(query, (name,))
        return results[0] if results else None

    def get_pokemon_species_by_id(self, species_id: int) -> Optional[Dict]:
        """根据ID获取宝可梦种类"""
        query = "SELECT * FROM pokemon_species WHERE id = %s"
        results = self.execute_query(query, (species_id,))
        return results[0] if results else None

    def search_pokemon_species(self, keyword: str) -> List[Dict]:
        """搜索宝可梦种类"""
        query = "SELECT * FROM pokemon_species WHERE name LIKE %s ORDER BY pokedex_number"
        return self.execute_query(query, (f"%{keyword}%",))

    def get_all_pokemon_species(self) -> List[Dict]:
        """获取所有宝可梦种类"""
        query = "SELECT * FROM pokemon_species ORDER BY pokedex_number"
        return self.execute_query(query)

    # 蛋组相关操作
    def get_all_egg_groups(self) -> List[Dict]:
        """获取所有蛋组"""
        query = "SELECT * FROM egg_groups ORDER BY name"
        return self.execute_query(query)

    def get_pokemon_egg_groups(self, pokemon_species_id: int) -> List[Dict]:
        """获取宝可梦的蛋组"""
        query = """
            SELECT eg.* FROM egg_groups eg
            JOIN pokemon_egg_group_relations pegr ON eg.id = pegr.egg_group_id
            WHERE pegr.pokemon_species_id = %s
        """
        return self.execute_query(query, (pokemon_species_id,))

    def add_pokemon_egg_group_relation(self, pokemon_species_id: int, egg_group_id: int) -> bool:
        """添加宝可梦蛋组关系"""
        query = "INSERT IGNORE INTO pokemon_egg_group_relations (pokemon_species_id, egg_group_id) VALUES (%s, %s)"
        success, _ = self.execute_update(query, (pokemon_species_id, egg_group_id))
        return success

    def remove_pokemon_egg_group_relations(self, pokemon_species_id: int) -> bool:
        """删除宝可梦的所有蛋组关系"""
        query = "DELETE FROM pokemon_egg_group_relations WHERE pokemon_species_id = %s"
        success, _ = self.execute_update(query, (pokemon_species_id,))
        return success

    # 宝可梦实例相关操作
    def add_pokemon_instance(self, pokemon_data: Dict) -> int:
        """添加宝可梦实例"""
        query = """
            INSERT INTO pokemon_instances
            (pokemon_species_id, nickname, gender, nature, hp_iv, attack_iv, defense_iv,
             sp_attack_iv, sp_defense_iv, speed_iv, price, is_shiny, notes)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (
            pokemon_data['pokemon_species_id'],
            pokemon_data.get('nickname'),
            pokemon_data.get('gender', '不明'),
            pokemon_data.get('nature', 'Hardy'),
            pokemon_data.get('hp_iv', 0),
            pokemon_data.get('attack_iv', 0),
            pokemon_data.get('defense_iv', 0),
            pokemon_data.get('sp_attack_iv', 0),
            pokemon_data.get('sp_defense_iv', 0),
            pokemon_data.get('speed_iv', 0),
            pokemon_data.get('price', 0),
            pokemon_data.get('is_shiny', False),
            pokemon_data.get('notes')
        )
        success, insert_id = self.execute_update(query, params)
        return insert_id if success else 0

    def get_pokemon_instances(self) -> List[Dict]:
        """获取所有宝可梦实例（包含种类信息）"""
        query = """
            SELECT pi.*, ps.name as species_name, ps.pokedex_number, ps.image_path
            FROM pokemon_instances pi
            JOIN pokemon_species ps ON pi.pokemon_species_id = ps.id
            ORDER BY ps.pokedex_number, pi.created_at
        """
        return self.execute_query(query)

    def update_pokemon_instance(self, instance_id: int, pokemon_data: Dict) -> bool:
        """更新宝可梦实例"""
        query = """
            UPDATE pokemon_instances
            SET nickname = %s, gender = %s, nature = %s, hp_iv = %s, attack_iv = %s,
                defense_iv = %s, sp_attack_iv = %s, sp_defense_iv = %s, speed_iv = %s,
                price = %s, is_shiny = %s, notes = %s
            WHERE id = %s
        """
        params = (
            pokemon_data.get('nickname'),
            pokemon_data.get('gender', '不明'),
            pokemon_data.get('nature', 'Hardy'),
            pokemon_data.get('hp_iv', 0),
            pokemon_data.get('attack_iv', 0),
            pokemon_data.get('defense_iv', 0),
            pokemon_data.get('sp_attack_iv', 0),
            pokemon_data.get('sp_defense_iv', 0),
            pokemon_data.get('speed_iv', 0),
            pokemon_data.get('price', 0),
            pokemon_data.get('is_shiny', False),
            pokemon_data.get('notes'),
            instance_id
        )
        success, _ = self.execute_update(query, params)
        return success

    def delete_pokemon_instance(self, instance_id: int) -> bool:
        """删除宝可梦实例"""
        query = "DELETE FROM pokemon_instances WHERE id = %s"
        success, _ = self.execute_update(query, (instance_id,))
        return success
