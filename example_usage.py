#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
POKEMMO 孵蛋计算器使用示例

这个脚本演示了如何使用数据库API来管理宝可梦数据
"""

from database import DatabaseManager
from pokemon_utils import scan_pokemon_images

def example_usage():
    """使用示例"""
    print("=== POKEMMO 孵蛋计算器使用示例 ===\n")
    
    # 初始化数据库
    db = DatabaseManager()
    
    print("1. 连接数据库...")
    if not db.connect():
        print("❌ 数据库连接失败")
        return
    print("✅ 数据库连接成功\n")
    
    # 查看宝可梦种类
    print("2. 查看已导入的宝可梦种类...")
    species_list = db.get_all_pokemon_species()
    print(f"共有 {len(species_list)} 个宝可梦种类")
    
    # 显示前5个
    for i, species in enumerate(species_list[:5]):
        print(f"  {i+1}. #{species['pokedex_number']:03d} {species['name']}")
    
    if len(species_list) > 5:
        print(f"  ... 还有 {len(species_list) - 5} 个")
    print()
    
    # 搜索宝可梦
    print("3. 搜索宝可梦...")
    search_results = db.search_pokemon_species("妙蛙")
    print(f"搜索'妙蛙'找到 {len(search_results)} 个结果:")
    for species in search_results:
        print(f"  - #{species['pokedex_number']:03d} {species['name']}")
    print()
    
    # 查看蛋组信息
    print("4. 查看蛋组信息...")
    egg_groups = db.get_all_egg_groups()
    print(f"共有 {len(egg_groups)} 个蛋组:")
    for group in egg_groups:
        print(f"  - {group['name']}: {group['description']}")
    print()
    
    # 添加示例宝可梦实例
    print("5. 添加示例宝可梦实例...")
    
    # 获取妙蛙种子的种类信息
    bulbasaur = db.get_pokemon_species_by_name("妙蛙种子")
    if bulbasaur:
        # 添加一个5V妙蛙种子
        pokemon_data = {
            'pokemon_species_id': bulbasaur['id'],
            'nickname': '小绿',
            'gender': '公',
            'nature': 'Modest',
            'hp_iv': 31,
            'attack_iv': 0,
            'defense_iv': 31,
            'sp_attack_iv': 31,
            'sp_defense_iv': 31,
            'speed_iv': 31,
            'price': 50000,
            'is_shiny': False,
            'notes': '特攻向5V妙蛙种子，用于繁殖'
        }
        
        instance_id = db.add_pokemon_instance(pokemon_data)
        if instance_id:
            print(f"✅ 成功添加宝可梦实例 (ID: {instance_id})")
        else:
            print("❌ 添加宝可梦实例失败")
    else:
        print("❌ 未找到妙蛙种子种类信息")
    print()
    
    # 查看宝可梦实例
    print("6. 查看宝可梦实例...")
    instances = db.get_pokemon_instances()
    print(f"共有 {len(instances)} 个宝可梦实例:")
    
    for instance in instances:
        v_count = sum(1 for iv in [
            instance['hp_iv'], instance['attack_iv'], instance['defense_iv'],
            instance['sp_attack_iv'], instance['sp_defense_iv'], instance['speed_iv']
        ] if iv == 31)
        
        nickname = instance['nickname'] or "无昵称"
        print(f"  - {instance['species_name']} ({nickname}) - {v_count}V - {instance['price']:,}元")
    print()
    
    # 关闭数据库连接
    db.disconnect()
    print("✅ 示例完成")

def setup_example_data():
    """设置示例数据"""
    print("=== 设置示例数据 ===\n")
    
    db = DatabaseManager()
    
    # 创建数据库和表
    print("创建数据库...")
    db.create_database_if_not_exists()
    
    print("连接数据库...")
    db.connect()
    
    print("创建表...")
    db.create_tables()
    
    print("初始化蛋组数据...")
    db.initialize_egg_groups()
    
    # 导入宝可梦种类
    print("扫描并导入宝可梦种类...")
    pokemon_data = scan_pokemon_images()
    imported_count = 0
    
    for data in pokemon_data:
        existing = db.get_pokemon_species_by_name(data['name'])
        if not existing:
            species_id = db.add_pokemon_species(
                data['pokedex_number'] or 0,
                data['name'],
                data['image_path']
            )
            if species_id:
                imported_count += 1
    
    print(f"导入了 {imported_count} 个宝可梦种类")
    
    # 为一些宝可梦设置蛋组（示例）
    print("设置示例蛋组关系...")
    
    # 获取蛋组ID
    monster_group = next((g for g in db.get_all_egg_groups() if g['name'] == '怪兽'), None)
    grass_group = next((g for g in db.get_all_egg_groups() if g['name'] == '植物'), None)
    
    if monster_group and grass_group:
        # 妙蛙种子系列 - 怪兽+植物
        for name in ['妙蛙种子', '妙蛙草', '妙蛙花']:
            species = db.get_pokemon_species_by_name(name)
            if species:
                db.add_pokemon_egg_group_relation(species['id'], monster_group['id'])
                db.add_pokemon_egg_group_relation(species['id'], grass_group['id'])
    
    db.disconnect()
    print("✅ 示例数据设置完成\n")

if __name__ == "__main__":
    # 首先设置示例数据
    setup_example_data()
    
    # 然后运行使用示例
    example_usage()
    
    print("\n🎉 现在可以运行主程序了:")
    print("python pokemmo_breeding_calculator.py")
