import re
import os
from typing import Tu<PERSON>, Optional, List, Dict

def parse_pokemon_filename(filename: str) -> <PERSON><PERSON>[Optional[int], Optional[str]]:
    """
    从文件名中解析图鉴编号和宝可梦名称
    
    Args:
        filename: 文件名，如 "001妙蛙种子.gif"
        
    Returns:
        Tuple[pokedex_number, pokemon_name] 或 (None, None) 如果解析失败
    """
    # 移除文件扩展名
    name_without_ext = os.path.splitext(filename)[0]
    
    # 使用正则表达式匹配图鉴编号和名称
    # 匹配模式：开头的数字（1-4位）+ 中文字符或其他字符
    pattern = r'^(\d{1,4})(.+)$'
    match = re.match(pattern, name_without_ext)
    
    if match:
        pokedex_number = int(match.group(1))
        pokemon_name = match.group(2).strip()
        return pokedex_number, pokemon_name
    
    # 如果没有数字开头，尝试直接使用文件名作为宝可梦名称
    if name_without_ext:
        return None, name_without_ext
    
    return None, None

def scan_pokemon_images(images_dir: str = "images") -> List[Dict]:
    """
    扫描images目录，解析所有宝可梦图片文件
    
    Args:
        images_dir: 图片目录路径
        
    Returns:
        List[Dict]: 包含图鉴编号、名称和图片路径的字典列表
    """
    pokemon_data = []
    
    if not os.path.exists(images_dir):
        return pokemon_data
    
    # 支持的图片格式
    supported_formats = {'.gif', '.png', '.jpg', '.jpeg'}
    
    for filename in os.listdir(images_dir):
        file_path = os.path.join(images_dir, filename)
        
        # 检查是否是文件且是支持的图片格式
        if os.path.isfile(file_path):
            file_ext = os.path.splitext(filename)[1].lower()
            if file_ext in supported_formats:
                pokedex_number, pokemon_name = parse_pokemon_filename(filename)
                
                if pokemon_name:  # 只有成功解析出名称才添加
                    pokemon_data.append({
                        'pokedex_number': pokedex_number,
                        'name': pokemon_name,
                        'image_path': file_path
                    })
    
    # 按图鉴编号排序，没有编号的放在最后
    pokemon_data.sort(key=lambda x: (x['pokedex_number'] is None, x['pokedex_number'] or 9999))
    
    return pokemon_data

def get_pokemon_v_count(ivs: Dict[str, int]) -> int:
    """
    计算宝可梦的V数（31个体值的数量）
    
    Args:
        ivs: 个体值字典
        
    Returns:
        int: V数
    """
    return sum(1 for iv in ivs.values() if iv == 31)

def format_pokemon_ivs(ivs: Dict[str, int]) -> str:
    """
    格式化个体值显示
    
    Args:
        ivs: 个体值字典
        
    Returns:
        str: 格式化的个体值字符串
    """
    iv_names = {
        'hp': 'HP',
        'attack': '攻击',
        'defense': '防御',
        'sp_attack': '特攻',
        'sp_defense': '特防',
        'speed': '速度'
    }
    
    formatted_ivs = []
    for attr, value in ivs.items():
        if attr in iv_names:
            formatted_ivs.append(f"{iv_names[attr]}: {value}")
    
    return ", ".join(formatted_ivs)

def get_nature_display_name(nature: str) -> str:
    """
    获取性格的显示名称
    
    Args:
        nature: 性格名称
        
    Returns:
        str: 带中文说明的性格名称
    """
    nature_map = {
        "Hardy": "Hardy (中性)",
        "Lonely": "Lonely (孤独)",
        "Brave": "Brave (勇敢)",
        "Adamant": "Adamant (固执)",
        "Naughty": "Naughty (顽皮)",
        "Bold": "Bold (大胆)",
        "Docile": "Docile (坦率)",
        "Relaxed": "Relaxed (悠闲)",
        "Impish": "Impish (淘气)",
        "Lax": "Lax (乐天)",
        "Timid": "Timid (胆小)",
        "Hasty": "Hasty (急躁)",
        "Serious": "Serious (认真)",
        "Jolly": "Jolly (爽朗)",
        "Naive": "Naive (天真)",
        "Modest": "Modest (内敛)",
        "Mild": "Mild (温和)",
        "Quiet": "Quiet (冷静)",
        "Bashful": "Bashful (害羞)",
        "Rash": "Rash (马虎)",
        "Calm": "Calm (温顺)",
        "Gentle": "Gentle (温和)",
        "Sassy": "Sassy (自大)",
        "Careful": "Careful (慎重)",
        "Quirky": "Quirky (怪异)"
    }
    
    return nature_map.get(nature, nature)

def parse_nature_from_display(display_name: str) -> str:
    """
    从显示名称中解析出纯性格名称
    
    Args:
        display_name: 显示名称，如 "Hardy (中性)"
        
    Returns:
        str: 纯性格名称，如 "Hardy"
    """
    if " (" in display_name:
        return display_name.split(" (")[0]
    return display_name

def validate_iv_value(value: int) -> bool:
    """
    验证个体值是否有效
    
    Args:
        value: 个体值
        
    Returns:
        bool: 是否有效
    """
    return 0 <= value <= 31

def calculate_breeding_compatibility(pokemon1_egg_groups: List[str], pokemon2_egg_groups: List[str]) -> bool:
    """
    计算两个宝可梦是否可以繁殖
    
    Args:
        pokemon1_egg_groups: 宝可梦1的蛋组列表
        pokemon2_egg_groups: 宝可梦2的蛋组列表
        
    Returns:
        bool: 是否可以繁殖
    """
    # 如果任一方包含"未发现"蛋组，则不能繁殖
    if "未发现" in pokemon1_egg_groups or "未发现" in pokemon2_egg_groups:
        return False
    
    # 如果两者有共同的蛋组，则可以繁殖
    return bool(set(pokemon1_egg_groups) & set(pokemon2_egg_groups))
