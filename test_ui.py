#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试UI组件
"""

import tkinter as tk
from ui_components import ModernButton, ToggleButton, apply_modern_theme

def test_ui():
    """测试UI组件"""
    root = tk.Tk()
    root.title("UI组件测试")
    root.geometry("400x300")
    
    # 应用主题
    apply_modern_theme(root)
    
    # 测试现代按钮
    btn1 = ModernButton(root, text="主要按钮", style="primary")
    btn1.pack(pady=10)
    
    btn2 = ModernButton(root, text="次要按钮", style="secondary")
    btn2.pack(pady=10)
    
    btn3 = ModernButton(root, text="成功按钮", style="success")
    btn3.pack(pady=10)
    
    btn4 = ModernButton(root, text="危险按钮", style="danger")
    btn4.pack(pady=10)
    
    # 测试切换按钮
    var = tk.IntVar(value=0)
    toggle_btn = ToggleButton(root, var)
    toggle_btn.pack(pady=10)
    
    # 显示变量值的标签
    label = tk.Label(root, text="值: 0")
    label.pack(pady=10)
    
    def update_label(*args):
        label.config(text=f"值: {var.get()}")
    
    var.trace("w", update_label)
    
    root.mainloop()

if __name__ == "__main__":
    test_ui()
