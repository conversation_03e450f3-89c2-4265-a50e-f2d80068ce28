#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
美化的UI组件库
类似Tailwind CSS的组件风格
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional, Callable
import math

# 现代化主题配置
class ModernTheme:
    """现代化主题配置类"""

    # 主色调配置
    COLORS = {
        # 主要颜色
        "primary": "#3b82f6",
        "primary_hover": "#2563eb",
        "primary_active": "#1d4ed8",
        "primary_light": "#dbeafe",

        # 次要颜色
        "secondary": "#6b7280",
        "secondary_hover": "#4b5563",
        "secondary_active": "#374151",
        "secondary_light": "#f3f4f6",

        # 状态颜色
        "success": "#10b981",
        "success_hover": "#059669",
        "success_light": "#d1fae5",

        "warning": "#f59e0b",
        "warning_hover": "#d97706",
        "warning_light": "#fef3c7",

        "danger": "#ef4444",
        "danger_hover": "#dc2626",
        "danger_light": "#fee2e2",

        "info": "#06b6d4",
        "info_hover": "#0891b2",
        "info_light": "#cffafe",

        # 中性颜色
        "white": "#ffffff",
        "gray_50": "#f9fafb",
        "gray_100": "#f3f4f6",
        "gray_200": "#e5e7eb",
        "gray_300": "#d1d5db",
        "gray_400": "#9ca3af",
        "gray_500": "#6b7280",
        "gray_600": "#4b5563",
        "gray_700": "#374151",
        "gray_800": "#1f2937",
        "gray_900": "#111827",

        # 背景颜色
        "bg_primary": "#ffffff",
        "bg_secondary": "#f9fafb",
        "bg_tertiary": "#f3f4f6",

        # 文字颜色
        "text_primary": "#1f2937",
        "text_secondary": "#6b7280",
        "text_tertiary": "#9ca3af",
        "text_inverse": "#ffffff",

        # 边框颜色
        "border_light": "#e5e7eb",
        "border_medium": "#d1d5db",
        "border_dark": "#9ca3af",
    }

    # 字体配置
    FONTS = {
        "default": ("Segoe UI", 9),
        "heading": ("Segoe UI", 12, "bold"),
        "title": ("Segoe UI", 16, "bold"),
        "large_title": ("Segoe UI", 20, "bold"),
        "small": ("Segoe UI", 8),
        "code": ("Consolas", 9),
    }

    # 尺寸配置
    SIZES = {
        "border_radius": 6,
        "button_height": 36,
        "input_height": 32,
        "card_padding": 16,
        "section_spacing": 20,
        "element_spacing": 10,
        "small_spacing": 5,
    }

    # 阴影配置
    SHADOWS = {
        "small": {"offset": (0, 1), "blur": 3, "color": "#00000010"},
        "medium": {"offset": (0, 4), "blur": 6, "color": "#00000015"},
        "large": {"offset": (0, 10), "blur": 15, "color": "#00000020"},
    }

class ModernButton(tk.Button):
    """现代化按钮组件 - 支持圆角、渐变、阴影效果"""

    def __init__(self, parent, text="", command=None, style="primary", size="medium", **kwargs):
        self.parent = parent
        self.text = text
        self.command = command
        self.style_type = style
        self.size = size
        self.is_hovered = False
        self.is_pressed = False

        # 获取样式配置
        self.theme = ModernTheme()
        self.setup_style_config()

        # 创建Canvas作为按钮基础
        super().__init__(
            parent,
            text=text,
            command=command,
            font=self.font,
            fg=self.fg_color,
            bg=self.bg_color,
            activebackground=self.hover_bg_color,
            activeforeground=self.fg_color,
            relief="flat",
            borderwidth=0,
            cursor="hand2",
            **kwargs
        )

        # 设置按钮尺寸
        self.configure(
            height=self.button_height,
            padx=self.padding_x,
            pady=self.padding_y
        )

        # 绑定事件
        self.bind("<Enter>", self.on_enter)
        self.bind("<Leave>", self.on_leave)
        self.bind("<Button-1>", self.on_press)
        self.bind("<ButtonRelease-1>", self.on_release)

    def setup_style_config(self):
        """设置样式配置"""
        # 颜色配置
        color_map = {
            "primary": (self.theme.COLORS["primary"], self.theme.COLORS["primary_hover"]),
            "secondary": (self.theme.COLORS["secondary"], self.theme.COLORS["secondary_hover"]),
            "success": (self.theme.COLORS["success"], self.theme.COLORS["success_hover"]),
            "warning": (self.theme.COLORS["warning"], self.theme.COLORS["warning_hover"]),
            "danger": (self.theme.COLORS["danger"], self.theme.COLORS["danger_hover"]),
            "info": (self.theme.COLORS["info"], self.theme.COLORS["info_hover"]),
        }

        self.bg_color, self.hover_bg_color = color_map.get(self.style_type, color_map["primary"])
        self.fg_color = self.theme.COLORS["text_inverse"]

        # 尺寸配置
        size_map = {
            "small": (24, 8, 4, self.theme.FONTS["small"]),
            "medium": (32, 12, 6, self.theme.FONTS["default"]),
            "large": (40, 16, 8, self.theme.FONTS["heading"]),
        }

        self.button_height, self.padding_x, self.padding_y, self.font = size_map.get(self.size, size_map["medium"])

    def on_enter(self, event):
        """鼠标进入事件"""
        self.is_hovered = True
        self.configure(bg=self.hover_bg_color)

    def on_leave(self, event):
        """鼠标离开事件"""
        self.is_hovered = False
        if not self.is_pressed:
            self.configure(bg=self.bg_color)

    def on_press(self, event):
        """鼠标按下事件"""
        self.is_pressed = True
        # 添加按下效果（稍微变暗）
        pressed_color = self.darken_color(self.hover_bg_color, 0.1)
        self.configure(bg=pressed_color)

    def on_release(self, event):
        """鼠标释放事件"""
        self.is_pressed = False
        if self.is_hovered:
            self.configure(bg=self.hover_bg_color)
        else:
            self.configure(bg=self.bg_color)

    def darken_color(self, color, factor):
        """使颜色变暗"""
        # 简单的颜色变暗算法
        if color.startswith('#'):
            color = color[1:]

        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)

        r = max(0, int(r * (1 - factor)))
        g = max(0, int(g * (1 - factor)))
        b = max(0, int(b * (1 - factor)))

        return f"#{r:02x}{g:02x}{b:02x}"

class LoadingSpinner(tk.Canvas):
    """加载动画组件"""

    def __init__(self, parent, size=32, color=None, **kwargs):
        self.theme = ModernTheme()
        self.size = size
        self.color = color or self.theme.COLORS["primary"]
        self.angle = 0
        self.is_spinning = False

        super().__init__(
            parent,
            width=size,
            height=size,
            bg=self.theme.COLORS["bg_primary"],
            highlightthickness=0,
            **kwargs
        )

        self.center_x = size // 2
        self.center_y = size // 2
        self.radius = size // 3

    def start_spinning(self):
        """开始旋转动画"""
        self.is_spinning = True
        self.spin()

    def stop_spinning(self):
        """停止旋转动画"""
        self.is_spinning = False

    def spin(self):
        """旋转动画"""
        if not self.is_spinning:
            return

        self.delete("all")

        # 绘制旋转的圆弧
        for i in range(8):
            start_angle = self.angle + i * 45
            alpha = 1.0 - (i * 0.1)

            # 计算颜色透明度（简化版）
            color = self.color
            if alpha < 1.0:
                # 简单的透明度模拟
                color = self.theme.COLORS["gray_300"]

            # 绘制小圆点
            x = self.center_x + self.radius * math.cos(math.radians(start_angle))
            y = self.center_y + self.radius * math.sin(math.radians(start_angle))

            self.create_oval(
                x - 2, y - 2, x + 2, y + 2,
                fill=color,
                outline=""
            )

        self.angle = (self.angle + 45) % 360
        self.after(100, self.spin)

class GradientFrame(tk.Canvas):
    """渐变背景框架"""

    def __init__(self, parent, width=200, height=100, color1="#3b82f6", color2="#1d4ed8", direction="vertical", **kwargs):
        super().__init__(parent, width=width, height=height, highlightthickness=0, **kwargs)

        self.width = width
        self.height = height
        self.color1 = color1
        self.color2 = color2
        self.direction = direction

        self.bind("<Configure>", self.on_resize)
        self.draw_gradient()

    def on_resize(self, event):
        """窗口大小改变时重绘渐变"""
        self.width = event.width
        self.height = event.height
        self.draw_gradient()

    def draw_gradient(self):
        """绘制渐变背景"""
        self.delete("all")

        # 解析颜色
        r1, g1, b1 = self.hex_to_rgb(self.color1)
        r2, g2, b2 = self.hex_to_rgb(self.color2)

        if self.direction == "vertical":
            steps = self.height
            for i in range(steps):
                ratio = i / steps
                r = int(r1 + (r2 - r1) * ratio)
                g = int(g1 + (g2 - g1) * ratio)
                b = int(b1 + (b2 - b1) * ratio)

                color = f"#{r:02x}{g:02x}{b:02x}"
                self.create_line(0, i, self.width, i, fill=color, width=1)
        else:  # horizontal
            steps = self.width
            for i in range(steps):
                ratio = i / steps
                r = int(r1 + (r2 - r1) * ratio)
                g = int(g1 + (g2 - g1) * ratio)
                b = int(b1 + (b2 - b1) * ratio)

                color = f"#{r:02x}{g:02x}{b:02x}"
                self.create_line(i, 0, i, self.height, fill=color, width=1)

    def hex_to_rgb(self, hex_color):
        """将十六进制颜色转换为RGB"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

class ModernFrame(tk.Frame):
    """现代化框架组件 - 支持圆角和阴影"""

    def __init__(self, parent, padding=16, shadow=True, rounded=True, **kwargs):
        self.theme = ModernTheme()

        # 设置默认样式
        default_kwargs = {
            "bg": self.theme.COLORS["bg_primary"],
            "relief": "flat",
            "borderwidth": 0,
        }
        default_kwargs.update(kwargs)

        super().__init__(parent, **default_kwargs)

        # 添加内边距
        if padding:
            self.configure(padx=padding, pady=padding)

        # 如果需要阴影效果，可以通过边框模拟
        if shadow:
            self.configure(
                relief="solid",
                borderwidth=1,
                highlightbackground=self.theme.COLORS["border_light"],
                highlightthickness=1
            )

class ModernLabelFrame(ttk.LabelFrame):
    """现代化标签框架"""
    
    def __init__(self, parent, text="", **kwargs):
        super().__init__(parent, text=text, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Modern.TLabelframe",
            background="#ffffff",
            borderwidth=1,
            relief="solid"
        )
        style.configure(
            "Modern.TLabelframe.Label",
            background="#ffffff",
            foreground="#1f2937",
            font=("Arial", 10, "bold")
        )
        self.configure(style="Modern.TLabelframe")

class ToggleButton(tk.Button):
    """切换按钮组件（用于个体值设置）"""
    
    def __init__(self, parent, variable, on_text="31", off_text="0", **kwargs):
        self.variable = variable
        self.on_text = on_text
        self.off_text = off_text
        
        # 颜色配置
        self.colors = {
            "on": {"bg": "#10b981", "fg": "white", "active_bg": "#059669"},
            "off": {"bg": "#6b7280", "fg": "white", "active_bg": "#4b5563"}
        }
        
        super().__init__(parent, command=self.toggle, **kwargs)
        self.update_appearance()
        
        # 监听变量变化
        self.variable.trace("w", lambda *args: self.update_appearance())
    
    def toggle(self):
        """切换状态"""
        current = self.variable.get()
        if current == 0:
            self.variable.set(31)
        else:
            self.variable.set(0)
    
    def update_appearance(self):
        """更新外观"""
        current = self.variable.get()
        if current == 31:
            color_scheme = self.colors["on"]
            text = self.on_text
        else:
            color_scheme = self.colors["off"]
            text = self.off_text
        
        self.configure(
            text=text,
            bg=color_scheme["bg"],
            fg=color_scheme["fg"],
            activebackground=color_scheme["active_bg"],
            activeforeground=color_scheme["fg"],
            relief="flat",
            borderwidth=0,
            font=("Arial", 9, "bold"),
            width=4,
            height=1
        )

class ModernTreeview(ttk.Treeview):
    """现代化树形视图"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Modern.Treeview",
            background="#ffffff",
            foreground="#1f2937",
            fieldbackground="#ffffff",
            borderwidth=1,
            relief="solid"
        )
        style.configure(
            "Modern.Treeview.Heading",
            background="#f3f4f6",
            foreground="#1f2937",
            font=("Arial", 9, "bold")
        )
        style.map(
            "Modern.Treeview",
            background=[("selected", "#3b82f6")],
            foreground=[("selected", "white")]
        )
        
        self.configure(style="Modern.Treeview")

class ModernEntry(tk.Entry):
    """现代化输入框 - 支持焦点效果和占位符"""

    def __init__(self, parent, placeholder="", **kwargs):
        self.theme = ModernTheme()
        self.placeholder = placeholder
        self.placeholder_active = False

        # 设置默认样式
        default_kwargs = {
            "font": self.theme.FONTS["default"],
            "bg": self.theme.COLORS["bg_primary"],
            "fg": self.theme.COLORS["text_primary"],
            "relief": "solid",
            "borderwidth": 1,
            "highlightthickness": 1,
            "highlightcolor": self.theme.COLORS["primary"],
            "highlightbackground": self.theme.COLORS["border_light"],
            "insertbackground": self.theme.COLORS["text_primary"],
        }
        default_kwargs.update(kwargs)

        super().__init__(parent, **default_kwargs)

        # 设置占位符
        if self.placeholder:
            self.set_placeholder()

        # 绑定焦点事件
        self.bind("<FocusIn>", self.on_focus_in)
        self.bind("<FocusOut>", self.on_focus_out)
        self.bind("<KeyPress>", self.on_key_press)

    def set_placeholder(self):
        """设置占位符"""
        self.placeholder_active = True
        self.configure(fg=self.theme.COLORS["text_tertiary"])
        self.insert(0, self.placeholder)

    def clear_placeholder(self):
        """清除占位符"""
        if self.placeholder_active:
            self.placeholder_active = False
            self.configure(fg=self.theme.COLORS["text_primary"])
            self.delete(0, tk.END)

    def on_focus_in(self, event):
        """获得焦点时"""
        self.configure(
            highlightcolor=self.theme.COLORS["primary"],
            highlightbackground=self.theme.COLORS["primary"]
        )
        self.clear_placeholder()

    def on_focus_out(self, event):
        """失去焦点时"""
        self.configure(
            highlightcolor=self.theme.COLORS["border_light"],
            highlightbackground=self.theme.COLORS["border_light"]
        )
        if not self.get() and self.placeholder:
            self.set_placeholder()

    def on_key_press(self, event):
        """按键时"""
        if self.placeholder_active:
            self.clear_placeholder()

class ModernCombobox(ttk.Combobox):
    """现代化下拉框"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Modern.TCombobox",
            fieldbackground="#ffffff",
            borderwidth=1,
            relief="solid",
            padding=8
        )
        
        self.configure(style="Modern.TCombobox")

class ModernSpinbox(ttk.Spinbox):
    """现代化数字输入框"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Modern.TSpinbox",
            fieldbackground="#ffffff",
            borderwidth=1,
            relief="solid",
            padding=8
        )
        
        self.configure(style="Modern.TSpinbox")

class CardFrame(tk.Frame):
    """卡片式框架"""
    
    def __init__(self, parent, title="", **kwargs):
        super().__init__(parent, **kwargs)
        
        # 设置卡片样式
        self.configure(
            bg="#ffffff",
            relief="solid",
            borderwidth=1,
            padx=16,
            pady=16
        )
        
        if title:
            title_label = tk.Label(
                self,
                text=title,
                bg="#ffffff",
                fg="#1f2937",
                font=("Arial", 12, "bold")
            )
            title_label.pack(anchor="w", pady=(0, 10))

class StatusBar(tk.Frame):
    """状态栏组件"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.configure(
            bg="#f3f4f6",
            relief="flat",
            borderwidth=1,
            height=30
        )
        
        self.status_label = tk.Label(
            self,
            text="就绪",
            bg="#f3f4f6",
            fg="#6b7280",
            font=("Arial", 9)
        )
        self.status_label.pack(side="left", padx=10, pady=5)
    
    def set_status(self, text: str, color: str = "#6b7280"):
        """设置状态文本"""
        self.status_label.configure(text=text, fg=color)

class PokemonCard(tk.Frame):
    """宝可梦卡片组件"""

    def __init__(self, parent, pokemon, on_click=None, **kwargs):
        super().__init__(parent, **kwargs)

        self.pokemon = pokemon
        self.on_click = on_click
        self.selected = False

        # 设置卡片样式
        self.configure(
            bg="#ffffff",
            relief="solid",
            borderwidth=1,
            width=120,
            height=150,
            cursor="hand2"
        )

        # 防止框架收缩
        self.pack_propagate(False)
        self.grid_propagate(False)

        # 创建内容
        self.create_content()

        # 绑定点击事件
        self.bind("<Button-1>", self.on_card_click)
        self.bind("<Enter>", self.on_enter)
        self.bind("<Leave>", self.on_leave)

        # 为所有子组件绑定事件
        for child in self.winfo_children():
            child.bind("<Button-1>", self.on_card_click)

    def create_content(self):
        """创建卡片内容"""
        # 图片区域
        self.image_frame = tk.Frame(self, bg="#ffffff", height=80)
        self.image_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
        self.image_frame.pack_propagate(False)

        self.image_label = tk.Label(
            self.image_frame,
            bg="#ffffff",
            text="📷",
            font=("Arial", 24),
            fg="#9ca3af"
        )
        self.image_label.pack(expand=True)

        # 名称
        name = self.pokemon.nickname or self.pokemon.species_name
        if len(name) > 8:
            name = name[:8] + "..."

        self.name_label = tk.Label(
            self,
            text=name,
            bg="#ffffff",
            fg="#1f2937",
            font=("Arial", 9, "bold")
        )
        self.name_label.pack(pady=(2, 0))

        # V数和状态
        info_frame = tk.Frame(self, bg="#ffffff")
        info_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

        # V数
        v_count = self.pokemon.get_v_count()
        v_label = tk.Label(
            info_frame,
            text=f"{v_count}V",
            bg="#10b981" if v_count >= 5 else "#6b7280",
            fg="white",
            font=("Arial", 8, "bold"),
            padx=4,
            pady=1
        )
        v_label.pack(side=tk.LEFT)

        # 锁定状态
        if self.pokemon.is_locked:
            lock_label = tk.Label(
                info_frame,
                text="🔒",
                bg="#ffffff",
                font=("Arial", 10)
            )
            lock_label.pack(side=tk.RIGHT)

        # 闪光状态
        if self.pokemon.is_shiny:
            shiny_label = tk.Label(
                info_frame,
                text="✨",
                bg="#ffffff",
                font=("Arial", 10)
            )
            shiny_label.pack(side=tk.RIGHT)

    def load_image(self, image_path):
        """加载宝可梦图片"""
        try:
            from PIL import Image, ImageTk
            img = Image.open(image_path)
            img = img.resize((60, 60), Image.LANCZOS)
            photo = ImageTk.PhotoImage(img)

            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo  # 保持引用
        except Exception as e:
            print(f"加载图片失败: {e}")

    def on_card_click(self, event):
        """卡片点击事件"""
        if self.on_click:
            self.on_click(self.pokemon)

    def on_enter(self, event):
        """鼠标进入"""
        if not self.selected:
            self.configure(bg="#f3f4f6", borderwidth=2, relief="solid")

    def on_leave(self, event):
        """鼠标离开"""
        if not self.selected:
            self.configure(bg="#ffffff", borderwidth=1, relief="solid")

    def set_selected(self, selected):
        """设置选中状态"""
        self.selected = selected
        if selected:
            self.configure(bg="#dbeafe", borderwidth=2, relief="solid")
        else:
            self.configure(bg="#ffffff", borderwidth=1, relief="solid")

class FilterPanel(tk.Frame):
    """筛选面板组件"""

    def __init__(self, parent, on_filter_change=None, **kwargs):
        super().__init__(parent, **kwargs)

        self.on_filter_change = on_filter_change
        self.configure(bg="#ffffff", relief="solid", borderwidth=1)

        # 创建筛选控件
        self.create_filters()

    def create_filters(self):
        """创建筛选控件"""
        # 标题
        title_label = tk.Label(
            self,
            text="筛选条件",
            bg="#ffffff",
            fg="#1f2937",
            font=("Arial", 12, "bold")
        )
        title_label.pack(anchor="w", padx=10, pady=(10, 5))

        # 搜索框
        search_frame = tk.Frame(self, bg="#ffffff")
        search_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(
            search_frame,
            text="搜索:",
            bg="#ffffff",
            fg="#374151",
            font=("Arial", 10)
        ).pack(side=tk.LEFT)

        self.search_var = tk.StringVar()
        self.search_entry = ModernEntry(search_frame, textvariable=self.search_var)
        self.search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        self.search_var.trace("w", self.on_filter_changed)

        # 蛋组筛选
        egg_group_frame = tk.Frame(self, bg="#ffffff")
        egg_group_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(
            egg_group_frame,
            text="蛋组:",
            bg="#ffffff",
            fg="#374151",
            font=("Arial", 10)
        ).pack(side=tk.LEFT)

        self.egg_group_var = tk.StringVar(value="全部")
        self.egg_group_combo = ModernCombobox(
            egg_group_frame,
            textvariable=self.egg_group_var,
            values=["全部"],
            state="readonly",
            width=15
        )
        self.egg_group_combo.pack(side=tk.LEFT, padx=(5, 0))
        self.egg_group_combo.bind("<<ComboboxSelected>>", self.on_filter_changed)

        # V数筛选
        v_frame = tk.Frame(self, bg="#ffffff")
        v_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(
            v_frame,
            text="V数:",
            bg="#ffffff",
            fg="#374151",
            font=("Arial", 10)
        ).pack(side=tk.LEFT)

        self.v_count_var = tk.StringVar(value="全部")
        self.v_count_combo = ModernCombobox(
            v_frame,
            textvariable=self.v_count_var,
            values=["全部", "0V", "1V", "2V", "3V", "4V", "5V", "6V"],
            state="readonly",
            width=8
        )
        self.v_count_combo.pack(side=tk.LEFT, padx=(5, 0))
        self.v_count_combo.bind("<<ComboboxSelected>>", self.on_filter_changed)

        # 状态筛选
        status_frame = tk.Frame(self, bg="#ffffff")
        status_frame.pack(fill=tk.X, padx=10, pady=5)

        self.show_locked_var = tk.BooleanVar(value=True)
        self.show_locked_check = ttk.Checkbutton(
            status_frame,
            text="显示锁定",
            variable=self.show_locked_var,
            command=self.on_filter_changed
        )
        self.show_locked_check.pack(side=tk.LEFT)

        self.show_shiny_var = tk.BooleanVar(value=True)
        self.show_shiny_check = ttk.Checkbutton(
            status_frame,
            text="显示闪光",
            variable=self.show_shiny_var,
            command=self.on_filter_changed
        )
        self.show_shiny_check.pack(side=tk.LEFT, padx=(10, 0))

        # 重置按钮
        reset_btn = ModernButton(
            self,
            text="重置筛选",
            command=self.reset_filters,
            style="secondary"
        )
        reset_btn.pack(pady=10)

    def on_filter_changed(self, *args):
        """筛选条件改变"""
        if self.on_filter_change:
            filters = self.get_filters()
            self.on_filter_change(filters)

    def get_filters(self):
        """获取当前筛选条件"""
        return {
            'search': self.search_var.get().strip(),
            'egg_group': self.egg_group_var.get(),
            'v_count': self.v_count_var.get(),
            'show_locked': self.show_locked_var.get(),
            'show_shiny': self.show_shiny_var.get()
        }

    def reset_filters(self):
        """重置筛选条件"""
        self.search_var.set("")
        self.egg_group_var.set("全部")
        self.v_count_var.set("全部")
        self.show_locked_var.set(True)
        self.show_shiny_var.set(True)

    def update_egg_groups(self, egg_groups):
        """更新蛋组选项"""
        values = ["全部"] + egg_groups
        self.egg_group_combo.configure(values=values)

def apply_modern_theme(root):
    """应用现代主题"""
    style = ttk.Style()

    # 设置主题
    try:
        style.theme_use('clam')
    except:
        pass

    # 配置全局样式
    style.configure(".", font=("Arial", 9))

    # 配置窗口背景
    root.configure(bg="#f9fafb")

    return style
