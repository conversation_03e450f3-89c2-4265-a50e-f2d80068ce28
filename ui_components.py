#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
美化的UI组件库
类似Tailwind CSS的组件风格
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional, Callable

class ModernButton(ttk.Button):
    """现代化按钮组件"""
    
    def __init__(self, parent, text="", command=None, style="primary", **kwargs):
        # 创建自定义样式
        self.style_obj = ttk.Style()
        
        # 定义颜色方案
        self.colors = {
            "primary": {
                "bg": "#3b82f6",
                "fg": "white",
                "hover_bg": "#2563eb",
                "active_bg": "#1d4ed8"
            },
            "secondary": {
                "bg": "#6b7280",
                "fg": "white", 
                "hover_bg": "#4b5563",
                "active_bg": "#374151"
            },
            "success": {
                "bg": "#10b981",
                "fg": "white",
                "hover_bg": "#059669",
                "active_bg": "#047857"
            },
            "danger": {
                "bg": "#ef4444",
                "fg": "white",
                "hover_bg": "#dc2626",
                "active_bg": "#b91c1c"
            },
            "warning": {
                "bg": "#f59e0b",
                "fg": "white",
                "hover_bg": "#d97706",
                "active_bg": "#b45309"
            }
        }
        
        # 设置样式
        style_name = f"Modern.{style.capitalize()}.TButton"
        color_scheme = self.colors.get(style, self.colors["primary"])
        
        self.style_obj.configure(
            style_name,
            background=color_scheme["bg"],
            foreground=color_scheme["fg"],
            borderwidth=0,
            focuscolor="none",
            padding=(12, 8)
        )
        
        self.style_obj.map(
            style_name,
            background=[
                ("active", color_scheme["active_bg"]),
                ("pressed", color_scheme["active_bg"]),
                ("!disabled", color_scheme["bg"])
            ],
            foreground=[("!disabled", color_scheme["fg"])]
        )
        
        super().__init__(parent, text=text, command=command, style=style_name, **kwargs)

class ModernFrame(ttk.Frame):
    """现代化框架组件"""
    
    def __init__(self, parent, padding=10, **kwargs):
        super().__init__(parent, padding=padding, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure("Modern.TFrame", background="#ffffff", relief="flat")
        self.configure(style="Modern.TFrame")

class ModernLabelFrame(ttk.LabelFrame):
    """现代化标签框架"""
    
    def __init__(self, parent, text="", **kwargs):
        super().__init__(parent, text=text, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Modern.TLabelframe",
            background="#ffffff",
            borderwidth=1,
            relief="solid"
        )
        style.configure(
            "Modern.TLabelframe.Label",
            background="#ffffff",
            foreground="#1f2937",
            font=("Arial", 10, "bold")
        )
        self.configure(style="Modern.TLabelframe")

class ToggleButton(tk.Button):
    """切换按钮组件（用于个体值设置）"""
    
    def __init__(self, parent, variable, on_text="31", off_text="0", **kwargs):
        self.variable = variable
        self.on_text = on_text
        self.off_text = off_text
        
        # 颜色配置
        self.colors = {
            "on": {"bg": "#10b981", "fg": "white", "active_bg": "#059669"},
            "off": {"bg": "#6b7280", "fg": "white", "active_bg": "#4b5563"}
        }
        
        super().__init__(parent, command=self.toggle, **kwargs)
        self.update_appearance()
        
        # 监听变量变化
        self.variable.trace("w", lambda *args: self.update_appearance())
    
    def toggle(self):
        """切换状态"""
        current = self.variable.get()
        if current == 0:
            self.variable.set(31)
        else:
            self.variable.set(0)
    
    def update_appearance(self):
        """更新外观"""
        current = self.variable.get()
        if current == 31:
            color_scheme = self.colors["on"]
            text = self.on_text
        else:
            color_scheme = self.colors["off"]
            text = self.off_text
        
        self.configure(
            text=text,
            bg=color_scheme["bg"],
            fg=color_scheme["fg"],
            activebackground=color_scheme["active_bg"],
            activeforeground=color_scheme["fg"],
            relief="flat",
            borderwidth=0,
            font=("Arial", 9, "bold"),
            width=4,
            height=1
        )

class ModernTreeview(ttk.Treeview):
    """现代化树形视图"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Modern.Treeview",
            background="#ffffff",
            foreground="#1f2937",
            fieldbackground="#ffffff",
            borderwidth=1,
            relief="solid"
        )
        style.configure(
            "Modern.Treeview.Heading",
            background="#f3f4f6",
            foreground="#1f2937",
            font=("Arial", 9, "bold")
        )
        style.map(
            "Modern.Treeview",
            background=[("selected", "#3b82f6")],
            foreground=[("selected", "white")]
        )
        
        self.configure(style="Modern.Treeview")

class ModernEntry(ttk.Entry):
    """现代化输入框"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Modern.TEntry",
            fieldbackground="#ffffff",
            borderwidth=1,
            relief="solid",
            padding=8
        )
        style.map(
            "Modern.TEntry",
            focuscolor=[("focus", "#3b82f6")]
        )
        
        self.configure(style="Modern.TEntry")

class ModernCombobox(ttk.Combobox):
    """现代化下拉框"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Modern.TCombobox",
            fieldbackground="#ffffff",
            borderwidth=1,
            relief="solid",
            padding=8
        )
        
        self.configure(style="Modern.TCombobox")

class ModernSpinbox(ttk.Spinbox):
    """现代化数字输入框"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Modern.TSpinbox",
            fieldbackground="#ffffff",
            borderwidth=1,
            relief="solid",
            padding=8
        )
        
        self.configure(style="Modern.TSpinbox")

class CardFrame(tk.Frame):
    """卡片式框架"""
    
    def __init__(self, parent, title="", **kwargs):
        super().__init__(parent, **kwargs)
        
        # 设置卡片样式
        self.configure(
            bg="#ffffff",
            relief="solid",
            borderwidth=1,
            padx=16,
            pady=16
        )
        
        if title:
            title_label = tk.Label(
                self,
                text=title,
                bg="#ffffff",
                fg="#1f2937",
                font=("Arial", 12, "bold")
            )
            title_label.pack(anchor="w", pady=(0, 10))

class StatusBar(tk.Frame):
    """状态栏组件"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.configure(
            bg="#f3f4f6",
            relief="flat",
            borderwidth=1,
            height=30
        )
        
        self.status_label = tk.Label(
            self,
            text="就绪",
            bg="#f3f4f6",
            fg="#6b7280",
            font=("Arial", 9)
        )
        self.status_label.pack(side="left", padx=10, pady=5)
    
    def set_status(self, text: str, color: str = "#6b7280"):
        """设置状态文本"""
        self.status_label.configure(text=text, fg=color)

class PokemonCard(tk.Frame):
    """宝可梦卡片组件"""

    def __init__(self, parent, pokemon, on_click=None, **kwargs):
        super().__init__(parent, **kwargs)

        self.pokemon = pokemon
        self.on_click = on_click
        self.selected = False

        # 设置卡片样式
        self.configure(
            bg="#ffffff",
            relief="solid",
            borderwidth=1,
            width=120,
            height=150,
            cursor="hand2"
        )

        # 防止框架收缩
        self.pack_propagate(False)
        self.grid_propagate(False)

        # 创建内容
        self.create_content()

        # 绑定点击事件
        self.bind("<Button-1>", self.on_card_click)
        self.bind("<Enter>", self.on_enter)
        self.bind("<Leave>", self.on_leave)

        # 为所有子组件绑定事件
        for child in self.winfo_children():
            child.bind("<Button-1>", self.on_card_click)

    def create_content(self):
        """创建卡片内容"""
        # 图片区域
        self.image_frame = tk.Frame(self, bg="#ffffff", height=80)
        self.image_frame.pack(fill=tk.X, padx=5, pady=(5, 0))
        self.image_frame.pack_propagate(False)

        self.image_label = tk.Label(
            self.image_frame,
            bg="#ffffff",
            text="📷",
            font=("Arial", 24),
            fg="#9ca3af"
        )
        self.image_label.pack(expand=True)

        # 名称
        name = self.pokemon.nickname or self.pokemon.species_name
        if len(name) > 8:
            name = name[:8] + "..."

        self.name_label = tk.Label(
            self,
            text=name,
            bg="#ffffff",
            fg="#1f2937",
            font=("Arial", 9, "bold")
        )
        self.name_label.pack(pady=(2, 0))

        # V数和状态
        info_frame = tk.Frame(self, bg="#ffffff")
        info_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

        # V数
        v_count = self.pokemon.get_v_count()
        v_label = tk.Label(
            info_frame,
            text=f"{v_count}V",
            bg="#10b981" if v_count >= 5 else "#6b7280",
            fg="white",
            font=("Arial", 8, "bold"),
            padx=4,
            pady=1
        )
        v_label.pack(side=tk.LEFT)

        # 锁定状态
        if self.pokemon.is_locked:
            lock_label = tk.Label(
                info_frame,
                text="🔒",
                bg="#ffffff",
                font=("Arial", 10)
            )
            lock_label.pack(side=tk.RIGHT)

        # 闪光状态
        if self.pokemon.is_shiny:
            shiny_label = tk.Label(
                info_frame,
                text="✨",
                bg="#ffffff",
                font=("Arial", 10)
            )
            shiny_label.pack(side=tk.RIGHT)

    def load_image(self, image_path):
        """加载宝可梦图片"""
        try:
            from PIL import Image, ImageTk
            img = Image.open(image_path)
            img = img.resize((60, 60), Image.LANCZOS)
            photo = ImageTk.PhotoImage(img)

            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo  # 保持引用
        except Exception as e:
            print(f"加载图片失败: {e}")

    def on_card_click(self, event):
        """卡片点击事件"""
        if self.on_click:
            self.on_click(self.pokemon)

    def on_enter(self, event):
        """鼠标进入"""
        if not self.selected:
            self.configure(bg="#f3f4f6", borderwidth=2, relief="solid")

    def on_leave(self, event):
        """鼠标离开"""
        if not self.selected:
            self.configure(bg="#ffffff", borderwidth=1, relief="solid")

    def set_selected(self, selected):
        """设置选中状态"""
        self.selected = selected
        if selected:
            self.configure(bg="#dbeafe", borderwidth=2, relief="solid")
        else:
            self.configure(bg="#ffffff", borderwidth=1, relief="solid")

class FilterPanel(tk.Frame):
    """筛选面板组件"""

    def __init__(self, parent, on_filter_change=None, **kwargs):
        super().__init__(parent, **kwargs)

        self.on_filter_change = on_filter_change
        self.configure(bg="#ffffff", relief="solid", borderwidth=1)

        # 创建筛选控件
        self.create_filters()

    def create_filters(self):
        """创建筛选控件"""
        # 标题
        title_label = tk.Label(
            self,
            text="筛选条件",
            bg="#ffffff",
            fg="#1f2937",
            font=("Arial", 12, "bold")
        )
        title_label.pack(anchor="w", padx=10, pady=(10, 5))

        # 搜索框
        search_frame = tk.Frame(self, bg="#ffffff")
        search_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(
            search_frame,
            text="搜索:",
            bg="#ffffff",
            fg="#374151",
            font=("Arial", 10)
        ).pack(side=tk.LEFT)

        self.search_var = tk.StringVar()
        self.search_entry = ModernEntry(search_frame, textvariable=self.search_var)
        self.search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))
        self.search_var.trace("w", self.on_filter_changed)

        # 蛋组筛选
        egg_group_frame = tk.Frame(self, bg="#ffffff")
        egg_group_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(
            egg_group_frame,
            text="蛋组:",
            bg="#ffffff",
            fg="#374151",
            font=("Arial", 10)
        ).pack(side=tk.LEFT)

        self.egg_group_var = tk.StringVar(value="全部")
        self.egg_group_combo = ModernCombobox(
            egg_group_frame,
            textvariable=self.egg_group_var,
            values=["全部"],
            state="readonly",
            width=15
        )
        self.egg_group_combo.pack(side=tk.LEFT, padx=(5, 0))
        self.egg_group_combo.bind("<<ComboboxSelected>>", self.on_filter_changed)

        # V数筛选
        v_frame = tk.Frame(self, bg="#ffffff")
        v_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(
            v_frame,
            text="V数:",
            bg="#ffffff",
            fg="#374151",
            font=("Arial", 10)
        ).pack(side=tk.LEFT)

        self.v_count_var = tk.StringVar(value="全部")
        self.v_count_combo = ModernCombobox(
            v_frame,
            textvariable=self.v_count_var,
            values=["全部", "0V", "1V", "2V", "3V", "4V", "5V", "6V"],
            state="readonly",
            width=8
        )
        self.v_count_combo.pack(side=tk.LEFT, padx=(5, 0))
        self.v_count_combo.bind("<<ComboboxSelected>>", self.on_filter_changed)

        # 状态筛选
        status_frame = tk.Frame(self, bg="#ffffff")
        status_frame.pack(fill=tk.X, padx=10, pady=5)

        self.show_locked_var = tk.BooleanVar(value=True)
        self.show_locked_check = ttk.Checkbutton(
            status_frame,
            text="显示锁定",
            variable=self.show_locked_var,
            command=self.on_filter_changed
        )
        self.show_locked_check.pack(side=tk.LEFT)

        self.show_shiny_var = tk.BooleanVar(value=True)
        self.show_shiny_check = ttk.Checkbutton(
            status_frame,
            text="显示闪光",
            variable=self.show_shiny_var,
            command=self.on_filter_changed
        )
        self.show_shiny_check.pack(side=tk.LEFT, padx=(10, 0))

        # 重置按钮
        reset_btn = ModernButton(
            self,
            text="重置筛选",
            command=self.reset_filters,
            style="secondary"
        )
        reset_btn.pack(pady=10)

    def on_filter_changed(self, *args):
        """筛选条件改变"""
        if self.on_filter_change:
            filters = self.get_filters()
            self.on_filter_change(filters)

    def get_filters(self):
        """获取当前筛选条件"""
        return {
            'search': self.search_var.get().strip(),
            'egg_group': self.egg_group_var.get(),
            'v_count': self.v_count_var.get(),
            'show_locked': self.show_locked_var.get(),
            'show_shiny': self.show_shiny_var.get()
        }

    def reset_filters(self):
        """重置筛选条件"""
        self.search_var.set("")
        self.egg_group_var.set("全部")
        self.v_count_var.set("全部")
        self.show_locked_var.set(True)
        self.show_shiny_var.set(True)

    def update_egg_groups(self, egg_groups):
        """更新蛋组选项"""
        values = ["全部"] + egg_groups
        self.egg_group_combo.configure(values=values)

def apply_modern_theme(root):
    """应用现代主题"""
    style = ttk.Style()

    # 设置主题
    try:
        style.theme_use('clam')
    except:
        pass

    # 配置全局样式
    style.configure(".", font=("Arial", 9))

    # 配置窗口背景
    root.configure(bg="#f9fafb")

    return style
