#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
美化的UI组件库
类似Tailwind CSS的组件风格
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, Any, Optional, Callable

class ModernButton(ttk.Button):
    """现代化按钮组件"""
    
    def __init__(self, parent, text="", command=None, style="primary", **kwargs):
        # 创建自定义样式
        self.style_obj = ttk.Style()
        
        # 定义颜色方案
        self.colors = {
            "primary": {
                "bg": "#3b82f6",
                "fg": "white",
                "hover_bg": "#2563eb",
                "active_bg": "#1d4ed8"
            },
            "secondary": {
                "bg": "#6b7280",
                "fg": "white", 
                "hover_bg": "#4b5563",
                "active_bg": "#374151"
            },
            "success": {
                "bg": "#10b981",
                "fg": "white",
                "hover_bg": "#059669",
                "active_bg": "#047857"
            },
            "danger": {
                "bg": "#ef4444",
                "fg": "white",
                "hover_bg": "#dc2626",
                "active_bg": "#b91c1c"
            },
            "warning": {
                "bg": "#f59e0b",
                "fg": "white",
                "hover_bg": "#d97706",
                "active_bg": "#b45309"
            }
        }
        
        # 设置样式
        style_name = f"Modern.{style.capitalize()}.TButton"
        color_scheme = self.colors.get(style, self.colors["primary"])
        
        self.style_obj.configure(
            style_name,
            background=color_scheme["bg"],
            foreground=color_scheme["fg"],
            borderwidth=0,
            focuscolor="none",
            padding=(12, 8)
        )
        
        self.style_obj.map(
            style_name,
            background=[
                ("active", color_scheme["active_bg"]),
                ("pressed", color_scheme["active_bg"]),
                ("!disabled", color_scheme["bg"])
            ],
            foreground=[("!disabled", color_scheme["fg"])]
        )
        
        super().__init__(parent, text=text, command=command, style=style_name, **kwargs)

class ModernFrame(ttk.Frame):
    """现代化框架组件"""
    
    def __init__(self, parent, padding=10, **kwargs):
        super().__init__(parent, padding=padding, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure("Modern.TFrame", background="#ffffff", relief="flat")
        self.configure(style="Modern.TFrame")

class ModernLabelFrame(ttk.LabelFrame):
    """现代化标签框架"""
    
    def __init__(self, parent, text="", **kwargs):
        super().__init__(parent, text=text, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Modern.TLabelframe",
            background="#ffffff",
            borderwidth=1,
            relief="solid"
        )
        style.configure(
            "Modern.TLabelframe.Label",
            background="#ffffff",
            foreground="#1f2937",
            font=("Arial", 10, "bold")
        )
        self.configure(style="Modern.TLabelframe")

class ToggleButton(tk.Button):
    """切换按钮组件（用于个体值设置）"""
    
    def __init__(self, parent, variable, on_text="31", off_text="0", **kwargs):
        self.variable = variable
        self.on_text = on_text
        self.off_text = off_text
        
        # 颜色配置
        self.colors = {
            "on": {"bg": "#10b981", "fg": "white", "active_bg": "#059669"},
            "off": {"bg": "#6b7280", "fg": "white", "active_bg": "#4b5563"}
        }
        
        super().__init__(parent, command=self.toggle, **kwargs)
        self.update_appearance()
        
        # 监听变量变化
        self.variable.trace("w", lambda *args: self.update_appearance())
    
    def toggle(self):
        """切换状态"""
        current = self.variable.get()
        if current == 0:
            self.variable.set(31)
        else:
            self.variable.set(0)
    
    def update_appearance(self):
        """更新外观"""
        current = self.variable.get()
        if current == 31:
            color_scheme = self.colors["on"]
            text = self.on_text
        else:
            color_scheme = self.colors["off"]
            text = self.off_text
        
        self.configure(
            text=text,
            bg=color_scheme["bg"],
            fg=color_scheme["fg"],
            activebackground=color_scheme["active_bg"],
            activeforeground=color_scheme["fg"],
            relief="flat",
            borderwidth=0,
            font=("Arial", 9, "bold"),
            width=4,
            height=1
        )

class ModernTreeview(ttk.Treeview):
    """现代化树形视图"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Modern.Treeview",
            background="#ffffff",
            foreground="#1f2937",
            fieldbackground="#ffffff",
            borderwidth=1,
            relief="solid"
        )
        style.configure(
            "Modern.Treeview.Heading",
            background="#f3f4f6",
            foreground="#1f2937",
            font=("Arial", 9, "bold")
        )
        style.map(
            "Modern.Treeview",
            background=[("selected", "#3b82f6")],
            foreground=[("selected", "white")]
        )
        
        self.configure(style="Modern.Treeview")

class ModernEntry(ttk.Entry):
    """现代化输入框"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Modern.TEntry",
            fieldbackground="#ffffff",
            borderwidth=1,
            relief="solid",
            padding=8
        )
        style.map(
            "Modern.TEntry",
            focuscolor=[("focus", "#3b82f6")]
        )
        
        self.configure(style="Modern.TEntry")

class ModernCombobox(ttk.Combobox):
    """现代化下拉框"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Modern.TCombobox",
            fieldbackground="#ffffff",
            borderwidth=1,
            relief="solid",
            padding=8
        )
        
        self.configure(style="Modern.TCombobox")

class ModernSpinbox(ttk.Spinbox):
    """现代化数字输入框"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        # 设置样式
        style = ttk.Style()
        style.configure(
            "Modern.TSpinbox",
            fieldbackground="#ffffff",
            borderwidth=1,
            relief="solid",
            padding=8
        )
        
        self.configure(style="Modern.TSpinbox")

class CardFrame(tk.Frame):
    """卡片式框架"""
    
    def __init__(self, parent, title="", **kwargs):
        super().__init__(parent, **kwargs)
        
        # 设置卡片样式
        self.configure(
            bg="#ffffff",
            relief="solid",
            borderwidth=1,
            padx=16,
            pady=16
        )
        
        if title:
            title_label = tk.Label(
                self,
                text=title,
                bg="#ffffff",
                fg="#1f2937",
                font=("Arial", 12, "bold")
            )
            title_label.pack(anchor="w", pady=(0, 10))

class StatusBar(tk.Frame):
    """状态栏组件"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.configure(
            bg="#f3f4f6",
            relief="flat",
            borderwidth=1,
            height=30
        )
        
        self.status_label = tk.Label(
            self,
            text="就绪",
            bg="#f3f4f6",
            fg="#6b7280",
            font=("Arial", 9)
        )
        self.status_label.pack(side="left", padx=10, pady=5)
    
    def set_status(self, text: str, color: str = "#6b7280"):
        """设置状态文本"""
        self.status_label.configure(text=text, fg=color)

def apply_modern_theme(root):
    """应用现代主题"""
    style = ttk.Style()
    
    # 设置主题
    try:
        style.theme_use('clam')
    except:
        pass
    
    # 配置全局样式
    style.configure(".", font=("Arial", 9))
    
    # 配置窗口背景
    root.configure(bg="#f9fafb")
    
    return style
